
// 该文件由脚本自动生成, 请勿修改.
export type InterfaceType = [{ path: "/api/demo/add"; method: "post"; input: { a: number; b: number; }; errorOutput: { status: "fail"; data: never; }; successOutput: { status: "success"; data: { res: number; }; }; webSocketData: {}; },{ path: "/api/demo/sub"; method: "post"; input: { a: number; b: number; }; errorOutput: { status: "fail"; data: "未登录"; }; successOutput: { status: "success"; data: { res: number; }; }; webSocketData: {}; },{ path: "/api/demo/upload-file"; method: "post"; input: {}; errorOutput: { status: "fail"; data: "未登录"; }; successOutput: { status: "success"; data: {}; }; webSocketData: {}; },{ path: "/api/demo/ws-test"; method: "post"; input: {}; errorOutput: { status: "fail"; data: never; }; successOutput: { status: "success"; data: {}; }; webSocketData: { data: string; }; },{ path: "/api/user/is-login"; method: "post"; input: {}; errorOutput: { status: "fail"; data: never; }; successOutput: { status: "success"; data: { isLogin: boolean; }; }; webSocketData: {}; },{ path: "/api/user/login"; method: "post"; input: { userName: string; } & { userPassword: string; }; errorOutput: { status: "fail"; data: "用户名不能包含空格" | "用户名不能为空" | "用户名过短" | "用户名过长" | "密码不能包含空格" | "密码不能为空" | "密码过短" | "密码过长" | "用户不存在或密码错误"; }; successOutput: { status: "success"; data: { token: string; }; }; webSocketData: {}; },{ path: "/api/user/register"; method: "post"; input: { userName: string; } & { userPassword: string; }; errorOutput: { status: "fail"; data: "用户名不能包含空格" | "用户名不能为空" | "用户名过短" | "用户名过长" | "密码不能包含空格" | "密码不能为空" | "密码过短" | "密码过长" | "用户名已存在"; }; successOutput: { status: "success"; data: {}; }; webSocketData: {}; },{ path: "/api/demo/get-list"; method: "post"; input: { page: number; size: number; }; errorOutput: { status: "fail"; data: never; }; successOutput: { status: "success"; data: { data: { value: string; id: string; }[]; total: number; }; }; webSocketData: {}; },{ path: "/api/funds/get-list"; method: "post"; input: {}; errorOutput: { status: "fail"; data: never; }; successOutput: { status: "success"; data: { windCode: string; fundName: string; fundCompany: string; fundType: string; issueDate: string; highCarbonRatio: number; fossilFuelRatio: number; fundSize: number; fundSizeUpdateDate: string; }[]; }; webSocketData: {}; },{ path: "/api/funds/get-options"; method: "post"; input: {}; errorOutput: { status: "fail"; data: never; }; successOutput: { status: "success"; data: { fundNames: { value: string; label: string; }[]; companies: string[]; fundTypes: string[]; }; }; webSocketData: {}; },{ path: "/api/funds/get-names"; method: "post"; input: {}; errorOutput: { status: "fail"; data: never; }; successOutput: { status: "success"; data: { fundNames: { value: string; label: string; }[]; }; }; webSocketData: {}; },{ path: "/api/funds/get-carbon-investment"; method: "post"; input: { windId: string; }; errorOutput: { status: "fail"; data: "基金代码不能为空"; }; successOutput: { status: "success"; data: { ffProportionInYearList: number[]; gtProportionInYearList: number[]; allYears: string[]; }; }; webSocketData: {}; },{ path: "/api/funds/get-years"; method: "post"; input: { windId: string; }; errorOutput: { status: "fail"; data: "基金代码不能为空"; }; successOutput: { status: "success"; data: string[]; }; webSocketData: {}; },{ path: "/api/funds/get-top-holdings"; method: "post"; input: { windId: string; year: string; }; errorOutput: { status: "fail"; data: "请选择基金" | "请选择年份"; }; successOutput: { status: "success"; data: { stockName: string; stockCode: string; proportiontototalstockinvestments: number; FFMark: string; GTMark: string; }[]; }; webSocketData: {}; },{ path: "/api/funds/get-stocks-list"; method: "post"; input: { windId: string; year: string; mark: "ff" | "gt" | "nt_ff" | "nt_gt"; paging?: { page: number; size: number; } | undefined; }; errorOutput: { status: "fail"; data: "请选择基金" | "请选择年份" | "请选择类型" | "页码不能小于1" | "每页数量不能为负数"; }; successOutput: { status: "success"; data: { list: { stockName: string; stockCode: string; proportiontototalstockinvestments: number; FFMark: string | null; GTMark: string | null; rptDate: string; windIndustry: string | null; gpType: string | null; }[]; pagination: { page: number; size: number; total: number; }; }; }; webSocketData: {}; },{ path: "/api/funds/download-home"; method: "post"; input: { fundType?: string | undefined; fundNames?: string[] | undefined; companies?: string[] | undefined; issueDateStart?: string | undefined; issueDateEnd?: string | undefined; }; errorOutput: { status: "fail"; data: never; }; successOutput: Buffer; webSocketData: {}; },{ path: "/api/funds/download-top-holdings"; method: "post"; input: { windId: string; year: string; }; errorOutput: { status: "fail"; data: never; }; successOutput: Buffer; webSocketData: {}; },{ path: "/api/funds/download-stock-list"; method: "post"; input: { windId: string; year: string; mark: "ff" | "gt" | "nt_ff" | "nt_gt"; }; errorOutput: { status: "fail"; data: never; }; successOutput: Buffer; webSocketData: {}; },{ path: "/api/funds/company-type-statistics"; method: "post"; input: { windId: string; year: string; }; errorOutput: { status: "fail"; data: never; }; successOutput: { status: "success"; data: { ff: { in: number; notIn: number; }; gt: { in: number; notIn: number; }; }; }; webSocketData: {}; }]

