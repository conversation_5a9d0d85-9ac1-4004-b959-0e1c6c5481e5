import { ExternalLink, Mail } from 'lucide-react'

export function PageFooter() {
  return (
    <footer className="bg-slate-900 text-white mt-[151px]">
      <div className="container">

        {/* Logo Section */}
        <div className="flex-shrink-0">
          <img alt="Greenpeace" className="my-4 h-6 pr-4" src="https://green-finance-monitor.greenpeace.org.cn/assets/greenpeace__logo--green-CTS8Zpoe.svg" />
        </div>
        <div className="mt-[130px] mb-[69px] mx-[59px] flex flex-col md:flex-row items-start justify-between gap-8">
          {/* Links Section */}
          <div className="flex-1 md:mx-12">
            <h3 className="text-green-400 font-semibold mb-4 flex items-center gap-2">
              <ExternalLink className="w-4 h-4" />
              友情链接
            </h3>
            <div className="flex gap-7">
              <a
                href="https://www.greenpeace.org.cn"
                target="_blank"
                rel="noopener noreferrer"
                className="block text-gray-300 hover:text-green-400 transition-colors"
              >
                绿色和平网
              </a>
              |
              <a
                href="#"
                className="block text-gray-300 hover:text-green-400 transition-colors"
              >
                绿色经济追踪平台
              </a>
            </div>
          </div>

          {/* Contact Section */}
          <div className="flex-shrink-0">
            <h3 className="text-green-400 font-semibold mb-4 flex items-center gap-2">
              <Mail className="w-4 h-4" />
              服务邮箱
            </h3>
            <a
              href="mailto:<EMAIL>"
              className="text-gray-300 hover:text-green-400 transition-colors"
            >
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </footer>
  )
}
