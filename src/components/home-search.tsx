import { useNavigate } from '@tanstack/react-router'
import { ChevronDownIcon, ChevronUpIcon, SearchIcon } from 'lucide-react'
import { useCallback, useEffect, useRef, useState } from 'react'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'
import { cn } from '@/helpers/utils'

interface HomeSearchProps {
  options?: Array<{ label: string, value: string }>
  onValueChange?: (value: string) => void
  placeholder?: string
  variant?: 'default' | 'inverted'
  animation?: number
  maxCount?: number
  className?: string
}

export function HomeSearch({
  options = [],
  onValueChange,
  placeholder = '请输入您想查询的基金名称',
  className,
}: HomeSearchProps) {
  const navigate = useNavigate()
  const [searchValue, setSearchValue] = useState('')
  const [selectedValue, setSelectedValue] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [highlightedIndex, setHighlightedIndex] = useState(-1)
  const [showAllOptions, setShowAllOptions] = useState(false)

  const inputRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const optionRefs = useRef<Array<HTMLDivElement | null>>([])

  // 过滤选项 - 当showAllOptions为true时显示所有选项
  const filteredOptions = showAllOptions
    ? options
    : options.filter(option =>
        option.label.toLowerCase().includes(searchValue.toLowerCase()),
      )

  // 滚动到选中的选项
  const scrollToSelectedOption = useCallback(() => {
    if (!selectedValue || !dropdownRef.current)
      return

    const selectedIndex = filteredOptions.findIndex(option => option.value === selectedValue)
    if (selectedIndex === -1)
      return

    const selectedElement = optionRefs.current[selectedIndex]
    if (selectedElement && dropdownRef.current) {
      selectedElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
      })
    }
  }, [selectedValue, filteredOptions])

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target
    setSearchValue(value)
    setIsOpen(true)
    setHighlightedIndex(-1)
    // 当用户开始输入时，切换到过滤模式
    setShowAllOptions(false)
  }

  // 处理选项选择
  const handleOptionSelect = (option: { label: string, value: string }) => {
    setSearchValue(option.label)
    setSelectedValue(option.value)
    setIsOpen(false)
    setHighlightedIndex(-1)
    setShowAllOptions(false)
    onValueChange?.(option.value)
  }

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen && (e.key === 'ArrowDown' || e.key === 'Enter')) {
      setIsOpen(true)
      setShowAllOptions(true)
      return
    }

    switch (e.key) {
      case 'ArrowDown': {
        e.preventDefault()
        setHighlightedIndex(prev =>
          prev < filteredOptions.length - 1 ? prev + 1 : prev,
        )
        break
      }
      case 'ArrowUp': {
        e.preventDefault()
        setHighlightedIndex(prev => prev > 0 ? prev - 1 : prev)
        break
      }
      case 'Enter': {
        e.preventDefault()
        if (highlightedIndex >= 0 && filteredOptions[highlightedIndex]) {
          handleOptionSelect(filteredOptions[highlightedIndex])
        }
        break
      }
      case 'Escape': {
        setIsOpen(false)
        setHighlightedIndex(-1)
        setShowAllOptions(false)
        inputRef.current?.blur()
        break
      }
    }
  }

  // 当下拉框打开且显示所有选项时，滚动到选中的选项
  useEffect(() => {
    if (isOpen && showAllOptions && selectedValue) {
      // 使用 setTimeout 确保 DOM 已经渲染
      const timeoutId = setTimeout(() => {
        scrollToSelectedOption()
      }, 0)

      return () => clearTimeout(timeoutId)
    }
  }, [isOpen, showAllOptions, selectedValue, scrollToSelectedOption])

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setHighlightedIndex(-1)
        setShowAllOptions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className={cn('relative mt-6 w-[700px]', className)} ref={dropdownRef}>
      {/* 搜索框容器 */}
      <div className="flex items-center h-[48px] bg-white rounded-[8px] shadow-[0_2px_8px_0_rgba(0,0,0,0.15)] overflow-hidden">
        {/* 搜索输入区域 */}
        <div className="flex items-center flex-1 px-4">
          <SearchIcon className="w-6 h-6 text-[#A6A6A6] mr-2 flex-shrink-0" />
          <input
            ref={inputRef}
            className="flex-1 outline-none border-none bg-transparent text-[#333] text-[18px] placeholder-[#A6A6A6]"
            placeholder={placeholder}
            value={searchValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => {
              setIsOpen(true)
              setShowAllOptions(true)
            }}
          />
          <Button
            variant="secondary"
            className="ml-2 p-1 hover:bg-gray-100 rounded transition-colors bg-transparent"
            onClick={() => {
              setIsOpen(!isOpen)
              if (!isOpen) {
                setShowAllOptions(true)
              }
            }}
          >
            {isOpen ? (
              <ChevronUpIcon className="w-5 h-5 text-[#A6A6A6]" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-[#A6A6A6]" />
            )}
          </Button>
        </div>

        {/* 斜角分隔线 */}
        <div className="relative h-full w-[16px] bg-white">
          <div
            className="absolute inset-0 bg-[#6EFF3E]"
            style={{
              clipPath: 'polygon(0 0, 100% 0, 100% 100%, 25% 100%)',
            }}
          />
        </div>

        {/* 查看基金详情按钮 */}
        <Button
          className="h-full px-6 bg-[#6EFF3E] text-[#222] text-[18px] font-medium whitespace-nowrap hover:bg-[#5be62e] transition-colors rounded-none"
          onClick={() => {
            if (!selectedValue) {
              toast.error('请输入基金名称')
              return
            }
            navigate({ to: '/detail', search: { id: selectedValue } })
          }}
        >
          查看基金详情
        </Button>
      </div>

      {/* 下拉选项列表 */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-[8px] shadow-[0_4px_12px_0_rgba(0,0,0,0.15)] z-50 max-h-[300px] overflow-y-auto">
          {filteredOptions.length > 0 ? (
            filteredOptions.map((option, index) => (
              <div
                key={option.value}
                ref={(el) => {
                  optionRefs.current[index] = el
                }}
                className={cn(
                  'px-4 py-3 cursor-pointer text-[16px] transition-colors border-b border-gray-100 last:border-b-0',
                  index === highlightedIndex
                    ? 'bg-[#44749D] text-white'
                    : 'text-[#333] hover:bg-gray-50',
                  selectedValue === option.value && 'bg-[#44749D] text-white',
                )}
                onClick={() => handleOptionSelect(option)}
                onMouseEnter={() => setHighlightedIndex(index)}
              >
                {option.label}
              </div>
            ))
          ) : (
            <div className="px-4 py-3 text-[#A6A6A6] text-center">
              没有找到匹配的基金
            </div>
          )}
        </div>
      )}
    </div>
  )
}
