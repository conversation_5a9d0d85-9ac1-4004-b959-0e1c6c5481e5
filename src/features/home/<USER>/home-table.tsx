import type {
  Cell,
  Column,
  <PERSON>umnD<PERSON>,
  Header,
  HeaderGroup,
  Row,
} from '@tanstack/react-table'
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { useAtomValue } from 'jotai'
import { ArrowDownWideNarrow } from 'lucide-react'
import { useMemo } from 'react'

import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

import type { FundInfo } from '../atoms'
import { getFundListQueryAtom, getFundOptionsQueryAtom } from '../atoms'

type SortDirection = 'asc' | 'desc' | false

function SearchButton({
  column,
  list,
}: {
  column: Column<FundInfo, unknown>
  list: Array<{ label: string, value: string }>
}) {
  return (
    <Select
      onValueChange={(value) => {
        // 根据选择的值进行筛选
        if (value === 'all') {
          column.setFilterValue(undefined)
        }
        else {
          column.setFilterValue(value)
        }
      }}
    >
      <SelectTrigger className="!h-[26px] w-full border-none focus:border-none focus:outline-none focus:ring-0 focus-visible:border-none focus-visible:outline-none focus-visible:ring-0 active:border-none hover:border-none data-[state=open]:border-none" iconClassName="!h-[13px] !w-[26px]" />

      <SelectContent>
        <SelectItem value="all">全部</SelectItem>
        {list.map(item => (
          <SelectItem key={item.value} value={item.value}>
            {item.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

function SortButton({
  column,
}: {
  column: Column<FundInfo, unknown>
}) {
  const sortDirection = column.getIsSorted() as SortDirection

  return (
    <Button
      variant="ghost"
      onClick={() => {
        if (!sortDirection) {
          column.toggleSorting(true)
        }
        else if (sortDirection === 'asc') {
          column.toggleSorting(false)
        }
        else {
          column.clearSorting()
        }
      }}
      className="h-8 w-8 p-0"
    >
      <ArrowDownWideNarrow className="h-4 w-4" />
    </Button>
  )
}

function CustomTableHeader({ title, children }: { title: string, children?: React.ReactNode }) {
  return (
    <div className="flex flex-col items-center justify-center pt-[17px] px-[9px] pb-[15px]">
      <div className="text-[20px] w-[109px] h-[64px] text-wrap text-center items-center flex justify-center">{title}</div>
      <div className="h-[23px]">{children}</div>
    </div>
  )
}

export function HomeTable({ handleReset: _handleReset }: { handleReset: () => void }) {
  const { data = [], isLoading } = useAtomValue(getFundListQueryAtom)
  const { data: fundOptions } = useAtomValue(getFundOptionsQueryAtom)

  const columns: Array<ColumnDef<FundInfo>> = useMemo(() => {
    const { companies = [], fundNames = [], fundTypes = [] } = fundOptions || {}
    const companieList = companies.map(name => ({ label: name, value: name }))
    const fundNamesList = fundNames || []
    const fundTypeList = fundTypes.map(name => ({ label: name, value: name }))
    return [
      {
        accessorKey: 'windCode',
        header: () => (
          <CustomTableHeader title="WIND代码" />
        ),

      },
      {
        accessorKey: 'fundName',
        header: ({ column }) => (
          <CustomTableHeader title="基金名称">
            <SearchButton column={column} list={fundNamesList} />
          </CustomTableHeader>
        ),
        filterFn: 'includesString',
      },
      {
        accessorKey: 'fundCompany',
        header: ({ column }) => (
          <CustomTableHeader title="基金公司">
            <SearchButton column={column} list={companieList} />
          </CustomTableHeader>
        ),
        filterFn: 'includesString',
      },
      {
        accessorKey: 'fundType',
        header: ({ column }) => (
          <CustomTableHeader title="基金类型">
            <SearchButton column={column} list={fundTypeList} />
          </CustomTableHeader>
        ),
        filterFn: 'includesString',
      },
      {
        accessorKey: 'issueDate',
        header: ({ column }) => (
          <CustomTableHeader title="发行日期">
            <SortButton column={column} />
          </CustomTableHeader>
        ),
        sortingFn: 'datetime',
      },
      {
        accessorKey: 'highCarbonRatio',
        header: ({ column }) => (
          <CustomTableHeader title="高碳相关投资比例">
            <SortButton column={column} />
          </CustomTableHeader>
        ),
      },
      {
        accessorKey: 'fossilFuelRatio',
        header: ({ column }) => (
          <CustomTableHeader title="化石燃料相关投资比例">
            <SortButton column={column} />
          </CustomTableHeader>
        ),
      },
      {
        accessorKey: 'fundSize',
        header: ({ column }) => (
          <CustomTableHeader title="基金规模（万元）">
            <SortButton column={column} />
          </CustomTableHeader>
        ),
      },
      {
        accessorKey: 'fundSizeUpdateDate',
        header: ({ column }) => (
          <CustomTableHeader title="基金规模更新日期">
            <SortButton column={column} />
          </CustomTableHeader>
        ),
        sortingFn: 'datetime',
      },
    ]
  }, [fundOptions])

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
  })

  return (
    <div className="rounded-md border">
      <div className="">
        <div className="overflow-auto max-h-[500px]">
          <Table>
            <TableHeader className="bg-[#001e3d] shadow sticky top-0 z-10">
              {table
                .getHeaderGroups()
                .map((headerGroup: HeaderGroup<FundInfo>) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map(
                      (header: Header<FundInfo, unknown>) => {
                        return (
                          <TableHead key={header.id} className="bg-[#001e3d] text-white border-b">
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                  header.column.columnDef.header,
                                  header.getContext(),
                                )}
                          </TableHead>
                        )
                      },
                    )}
                  </TableRow>
                ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    Loading...
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows?.length
                ? (
                    table.getRowModel().rows.map((row: Row<FundInfo>) => (
                      <TableRow
                        key={row.id}
                        data-state={row.getIsSelected() && 'selected'}
                      >
                        {row
                          .getVisibleCells()
                          .map((cell: Cell<FundInfo, unknown>) => (
                            <TableCell key={cell.id} className="text-center w-[124px] p-5 h-[104px] text-wrap whitespace-normal text-[22px]">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </TableCell>
                          ))}
                      </TableRow>
                    ))
                  )
                : (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-24 text-center"
                      >
                        No results.
                      </TableCell>
                    </TableRow>
                  )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
}
