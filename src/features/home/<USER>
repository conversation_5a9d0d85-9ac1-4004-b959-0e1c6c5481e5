import { useAtomValue } from 'jotai'
import { ArrowDownIcon } from 'lucide-react'

import { ResetIcon } from '@/components/icons/reset-icon'
import { PageFooter } from '@/components/page-footer'
import { PageHeader } from '@/components/page-header'
import { But<PERSON> } from '@/components/ui/button'
import type { POST_API_OUTPUTS } from '@/helpers/lib/api-client/types'

import type { QueryParams } from './atoms'
import { downloadHomeList, queryFormAtom } from './atoms'
import { HomeBanner } from './components/home-banner'
import { HomeTable } from './components/home-table'
import { useDownload } from './hooks/useDownload'

export function HomePage() {
  const queryParams = useAtomValue(queryFormAtom)
  const fileName = `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()} Finance of Future Fund List.xlsx`
  const { isPending, download } = useDownload<QueryParams, POST_API_OUTPUTS<'/api/funds/download-home'>>(downloadHomeList, fileName)

  async function handleDownload() {
    await download(queryParams)
  }

  function handleReset() {
    // TODO: 重置查询条件
  }

  return (
    <div className="flex min-h-screen flex-col">
      <div className="relative bg-[url('/src/assets/home-banner.jpg')] bg-cover bg-center bg-no-repeat h-[800px]">
        <PageHeader />
        <div className="container">
          <HomeBanner />
        </div>
      </div>
      <div className="p-6">
        {/* <div> */}
        {/*   <HomeForm /> */}
        {/* </div> */}
        <div className="container">
          <HomeTable handleReset={handleReset} />
        </div>
        <div className="container">
          <div className="mt-6 flex justify-between">
            <Button onClick={handleReset} variant="ghost">
              <ResetIcon className="!size-[38px]" />
              <span className="text-[28px]">重置</span>
            </Button>
            <Button
              onClick={handleDownload}
              disabled={isPending}
              className="bg-[#001e3d] text-white font-normal tracking-normal hover:bg-[#002a4d] disabled:opacity-50 rounded-none"
            >
              <ArrowDownIcon className="w-6 h-6" />
              <span className="">{isPending ? '下载中...' : '下载数据'}</span>
            </Button>
          </div>
        </div>
      </div>
      <PageFooter />
    </div>
  )
}
